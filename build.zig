const std = @import("std");
const zine = @import("zine");

pub fn build(b: *std.Build) !void {
    zine.website(b, .{
        .title = "Zig 语言中文社区",
        .host_url = "https://ziglang.cc",
        .content_dir_path = "content",
        .layouts_dir_path = "layouts",
        .assets_dir_path = "assets",
        .static_assets = &.{
            "favicons/favicon.ico",
            "favicons/apple-touch-icon.png",
            "favicons/android-chrome-192x192.png",
            "favicons/android-chrome-512x512.png",
            "favicons/android-chrome-maskable-192x192.png",
            "favicons/android-chrome-maskable-512x512.png",
            "images/",
        },
    });
}
